package cn.iocoder.yudao.module.mall.util.fronttask;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.servlet.ServletUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncFrontTask;
import com.alibaba.ttl.TtlRunnable;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import static cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants.UNAUTHORIZED;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * <AUTHOR>
 * @date 2024/8/16
 */
@Component
@Aspect
@Order(5002)
@Slf4j
public class AsyncFrontTaskAspect {

    @Resource
    private AsyncFrontTaskUtils asyncFrontTaskUtils;
    @Resource
    private HttpServletResponse response;

    @Around("@annotation(asyncFrontTask)")
    public Object around(ProceedingJoinPoint joinPoint, AsyncFrontTask asyncFrontTask) throws Throwable {
        if (SecurityFrameworkUtils.getLoginUser() == null) {
            throw exception(UNAUTHORIZED);
        }

        String taskId = AsyncFrontTaskUtils.generateTaskId();
        try {
            AsyncTaskTypeEnum taskType = asyncFrontTask.taskType();
            if(taskType == null) {
                throw new RuntimeException("taskType不能为空");
            }
            AsyncFrontTaskContext.setTaskId(taskId);
            // 非内嵌执行时，会自动封装成子线程任务
            if(!asyncFrontTask.isNested()) {
                TtlRunnable runnable = TtlRunnable.get(() -> {
                    try {
                        joinPoint.proceed();
                    } catch (Throwable e) {
                        log.error("ayncTask error: ", e);
                    } finally {
                        clean();
                    }
                });
                // 前端异步任务，开启任务后，直接返回任务ID
                String resultTaskId;
                if (asyncFrontTask.returnRunningTaskId()) {
                    // 如果有任务正在运行，返回正在运行的taskId
                    resultTaskId = asyncFrontTaskUtils.asyncTaskWithRunningCheck(SecurityFrameworkUtils.getLoginUserId(), taskId, taskType, runnable);
                } else {
                    // 如果有任务正在运行，抛出异常
                    asyncFrontTaskUtils.asyncTask(SecurityFrameworkUtils.getLoginUserId(), taskId, taskType, runnable);
                    resultTaskId = taskId;
                }
                ServletUtils.writeJSON(response, CommonResult.success(resultTaskId));
                return null;
            } else {
                // 内嵌执行时也需要检查重复任务
                if (asyncFrontTask.returnRunningTaskId()) {
                    // 检查是否有相同任务正在运行
                    String lockKey = AsyncFrontTask.genLockKey(SecurityFrameworkUtils.getLoginUserId(),
                        taskType.getTaskCode(), taskType.getType());
                    Object existingTaskId = asyncFrontTaskUtils.getRedisTemplate().opsForValue().get(lockKey);
                    if (existingTaskId != null) {
                        // 有任务正在运行，直接返回正在运行的taskId
                        log.info("检测到正在运行的内嵌任务，taskId: {}, userId: {}, taskType: {}",
                            existingTaskId, SecurityFrameworkUtils.getLoginUserId(), taskType.getTaskCode());
                        return CommonResult.success(existingTaskId.toString());
                    }
                }
                return joinPoint.proceed();
            }
        } catch (Exception e) {
            clean();
            throw e;
        }
    }

    private void clean() {
        try {
            AsyncFrontTaskContext.clear();
        } catch(Exception e) {
            log.error("async task context clean error: ", e);
        }
    }

}
