package cn.iocoder.yudao.module.mall.util.fronttask;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil;
import cn.iocoder.yudao.module.mall.enums.ErrorCodeConstants;
import cn.iocoder.yudao.module.mall.util.ResponseHelper;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncExportTask;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncFrontTask;
import cn.iocoder.yudao.module.mall.util.fronttask.vo.AsyncImportTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.Assert;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.Collection;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 文件异步导出工具类
 * <AUTHOR>
 * @date 2024/07/24
 */
@Component
@Slf4j
public class AsyncFrontTaskUtils {

    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private ThreadPoolExecutor threadPoolExecutor;
    @Resource
    @Lazy
    private ResponseHelper responseHelper;

    /**
     * 执行异步任务
     * @param userId
     * @param taskTypeEnum
     * @param runnable
     * @return
     */
    public void asyncTask(Long userId, AsyncTaskTypeEnum taskTypeEnum, Runnable runnable) {
        asyncTask(userId, AsyncFrontTaskContext.getTaskId(), taskTypeEnum, runnable);
    }

    /**
     * 执行异步任务
     * @param userId
     * @param taskId
     * @param taskTypeEnum
     * @param runnable
     * @return
     */
    public void asyncTask(Long userId, String taskId, AsyncTaskTypeEnum taskTypeEnum, Runnable runnable) {
        String lockKey = AsyncFrontTask.genLockKey(userId, taskTypeEnum.getTaskCode(), taskTypeEnum.getType());
        if(!redisTemplate.opsForValue().setIfAbsent(lockKey, "1", Duration.ofHours(2))) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.EXPORT_TASK_REPEAT_ERROR);
        }
        String cacheKey = AsyncFrontTask.genTaskCacheKey(taskId, taskTypeEnum.getType());
        AsyncFrontTask task = taskTypeEnum.isExport() ? new AsyncExportTask() : new AsyncImportTask();
        task.init(userId, taskId, taskTypeEnum);
        redisTemplate.opsForValue().set(cacheKey, task, Duration.ofHours(1));
        doRunnable(runnable);
    }

    /**
     * 执行异步任务，如果有任务正在运行则返回正在运行的taskId
     * @param userId
     * @param taskId
     * @param taskTypeEnum
     * @param runnable
     * @return 返回taskId，如果是新任务返回传入的taskId，如果有正在运行的任务返回正在运行的taskId
     */
    public String asyncTaskWithRunningCheck(Long userId, String taskId, AsyncTaskTypeEnum taskTypeEnum, Runnable runnable) {
        String lockKey = AsyncFrontTask.genLockKey(userId, taskTypeEnum.getTaskCode(), taskTypeEnum.getType());
        if(!redisTemplate.opsForValue().setIfAbsent(lockKey, taskId, Duration.ofHours(2))) {
            // 锁已存在，获取正在运行的taskId
            String runningTaskId = (String) redisTemplate.opsForValue().get(lockKey);
            if (runningTaskId != null) {
                log.info("检测到正在运行的任务，taskId: {}, userId: {}, taskType: {}", runningTaskId, userId, taskTypeEnum.getTaskCode());
                return runningTaskId;
            } else {
                // 锁存在但值为空，可能是旧版本的锁，抛出异常
                throw ServiceExceptionUtil.exception(ErrorCodeConstants.EXPORT_TASK_REPEAT_ERROR);
            }
        }

        // 成功获取锁，创建新任务
        String cacheKey = AsyncFrontTask.genTaskCacheKey(taskId, taskTypeEnum.getType());
        AsyncFrontTask task = taskTypeEnum.isExport() ? new AsyncExportTask() : new AsyncImportTask();
        task.init(userId, taskId, taskTypeEnum);
        redisTemplate.opsForValue().set(cacheKey, task, Duration.ofHours(1));
        doRunnable(runnable);
        return taskId;
    }

    private void doRunnable(Runnable runnable) {
        try {
            threadPoolExecutor.execute(runnable);
        } catch (Exception e) {
            log.error("Failed to execute runnable", e);
            throw e;
        }
    }

    /**
     * 更新异步导出任务完成进度
     * @param percent
     * @param fileUrl
     */
    public boolean updateExportProgress(Integer percent, String fileUrl) {
        String taskId = AsyncFrontTaskContext.getTaskId();
        Assert.notNull(taskId, "taskId is null");
        String cacheKey = AsyncFrontTask.genTaskCacheKey(taskId, 1);
        AsyncExportTask task = (AsyncExportTask) redisTemplate.opsForValue().get(cacheKey);
        if(task == null) {
            return false;
        }
        task.setProgress(percent);
        if(percent >= 100) {
            task.done().setDownloadUrl(fileUrl);
        }
        redisTemplate.opsForValue().set(cacheKey, task, Duration.ofHours(4));
        return true;
    }

    /**
     * 累加异步导出任务完成进度
     * @param curCount 当前处理记录数
     * @param totalCount 总记录数
     * @param weight 总权重
     */
    public boolean plusExportProgress(Integer curCount, Long totalCount, Integer weight) {
        int progress = (int)(curCount * 1.0f / totalCount * weight);
        return plusExportProgress(progress);
    }

    /**
     * 累加异步导出任务完成进度
     * @param percent
     */
    public boolean plusExportProgress(Integer percent) {
        String taskId = AsyncFrontTaskContext.getTaskId();
        Assert.notNull(taskId, "taskId is null");
        String cacheKey = AsyncFrontTask.genTaskCacheKey(taskId, 1);
        AsyncExportTask task = (AsyncExportTask) redisTemplate.opsForValue().get(cacheKey);
        if(task == null) {
            return false;
        }
        Integer updatePercent = task.getProgress() + percent;
        if(updatePercent >= 100) {
            updatePercent = 99;
        }
        task.setProgress(updatePercent);
        redisTemplate.opsForValue().set(cacheKey, task, Duration.ofHours(4));
        return true;
    }

    /**
     * 更新异步任务
     * @param task
     * @return
     */
    public boolean updateTask(AsyncFrontTask task) {
        Integer type = 1;
        if(task instanceof AsyncImportTask) {
            type = 2;
        }
        String cacheKey = AsyncFrontTask.genTaskCacheKey(task.getTaskId(), type);
        redisTemplate.opsForValue().set(cacheKey, task, Duration.ofHours(4));
        return true;
    }

    public String exportDone(String taskId, Collection<?> collection) {
        AsyncExportTask task = getAsyncExportTask(taskId);
        return taskDone(task, collection, 1);
    }

    public String importDone(String taskId, Collection<?> collection) {
        AsyncImportTask task = getAsyncImportTask(taskId);
        return taskDone(task, collection, 2);
    }

    public String taskDone(AsyncFrontTask task, Collection<?> collection, Integer type) {
        String taskId = task.getTaskId();
        String fileUrl = null;
        if(CollUtil.isNotEmpty(collection)) {
            log.info("异步任务生成导出文件开始, {}", task.getTaskId());
            String filePath = String.format("%s_%s.xlsx", task.getTaskName(), taskId);
            Class clazz = IterUtil.getElementType(collection);
            fileUrl = responseHelper.export2FileUrl(filePath, clazz, collection, false);
            log.info("异步任务生成导出文件完成, {}", task.getTaskId());
        }

        task.done().setDownloadUrl(fileUrl);
        updateTask(task);
        redisTemplate.delete(task.getLockKey(type));
        log.info("异步任务执行完成，更新状态:{}", task);

        return fileUrl;
    }

    public void taskDone(AsyncFrontTask task) {
        task.done();
        updateTask(task);
    }

    /**
     * 导出失败处理
     */
    public void exportFail() {
        handleFail(AsyncFrontTaskContext.getTaskId(), true);
    }

    /**
     * 导入失败处理
     */
    public void importFail() {
        handleFail(AsyncFrontTaskContext.getTaskId(), false);
    }

    private void handleFail(String taskId, boolean isExport) {
        Integer type = isExport ? 1 : 2;
        String cacheKey = AsyncFrontTask.genTaskCacheKey(taskId, type);
        AsyncFrontTask task = getAsyncFrontTask(taskId, type);
        task.setStatus(2);
        redisTemplate.opsForValue().set(cacheKey, task, Duration.ofMinutes(2));
        redisTemplate.delete(task.getLockKey(type));
    }

    /**
     * 查询异步导出任务
     * @param taskId
     * @return
     */
    public AsyncExportTask getAsyncExportTask(String taskId) {
        return getAsyncFrontTask(taskId, 1);
    }

    /**
     * 查询异步导入任务
     * @param taskId
     * @return
     */
    public AsyncImportTask getAsyncImportTask(String taskId) {
        return getAsyncFrontTask(taskId, 2);
    }

    /**
     * 查询异步任务
     * @param taskId
     * @return
     */
    public <T extends AsyncFrontTask> T getAsyncFrontTask(String taskId, Integer type) {
        String cacheKey = AsyncFrontTask.genTaskCacheKey(taskId, type);
        return (T) redisTemplate.opsForValue().get(cacheKey);
    }

    public static String generateTaskId() {
        return IdUtil.getSnowflakeNextIdStr();
    }

    /**
     * 获取RedisTemplate，供其他组件使用
     */
    public RedisTemplate<String, Object> getRedisTemplate() {
        return redisTemplate;
    }

}
