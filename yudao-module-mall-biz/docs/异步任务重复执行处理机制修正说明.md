# 异步任务重复执行处理机制修正说明

## 问题描述

在实现导入京东分类映射Excel的重复任务处理时，发现了一个关键问题：

**问题**：当有相同任务正在运行时，代码执行了`return joinPoint.proceed();`，并没有返回正在运行的任务ID。

## 根本原因

1. **注解配置问题**：`@AsyncFrontTask`注解默认`isNested = true`（内嵌执行）
2. **逻辑分支问题**：内嵌执行时直接调用`joinPoint.proceed()`，没有检查重复任务

## 修正方案

### 1. 修正注解配置

**修正前：**
```java
@AsyncFrontTask(taskType = AsyncTaskTypeEnum.PRODUCT_VOP_CATEGORY_MAPPING_IMPORT, returnRunningTaskId = true)
```

**修正后：**
```java
@AsyncFrontTask(
    taskType = AsyncTaskTypeEnum.PRODUCT_VOP_CATEGORY_MAPPING_IMPORT, 
    returnRunningTaskId = true,
    isNested = false  // 关键修正：启用异步处理
)
```

### 2. 增强内嵌执行的重复检查

在`AsyncFrontTaskAspect`中添加了内嵌执行时的重复任务检查：

```java
} else {
    // 内嵌执行时也需要检查重复任务
    if (asyncFrontTask.returnRunningTaskId()) {
        // 检查是否有相同任务正在运行
        String lockKey = AsyncFrontTask.genLockKey(SecurityFrameworkUtils.getLoginUserId(), 
            taskType.getTaskCode(), taskType.getType());
        Object existingTaskId = asyncFrontTaskUtils.getRedisTemplate().opsForValue().get(lockKey);
        if (existingTaskId != null) {
            // 有任务正在运行，直接返回正在运行的taskId
            log.info("检测到正在运行的内嵌任务，taskId: {}", existingTaskId);
            return CommonResult.success(existingTaskId.toString());
        }
    }
    return joinPoint.proceed();
}
```

## 修正后的完整流程

### 1. 非内嵌执行（isNested = false）

```
用户请求 -> AsyncFrontTaskAspect -> asyncTaskWithRunningCheck()
                                    |
                                   锁存在？-> 返回运行中taskId
                                    |
                                   否 -> 创建异步任务 -> 返回新taskId
```

### 2. 内嵌执行（isNested = true）

```
用户请求 -> AsyncFrontTaskAspect -> 检查returnRunningTaskId
                                    |
                                   true -> 检查锁 -> 锁存在？-> 返回运行中taskId
                                    |                |
                                   false            否 -> joinPoint.proceed()
                                    |
                                   joinPoint.proceed()
```

## 关键修正点

### 1. 注解配置

```java
// 京东分类映射导入 - 使用非内嵌执行
@AsyncFrontTask(
    taskType = AsyncTaskTypeEnum.PRODUCT_VOP_CATEGORY_MAPPING_IMPORT,
    returnRunningTaskId = true,
    isNested = false  // 必须设置为false
)
```

### 2. 暴露RedisTemplate

在`AsyncFrontTaskUtils`中添加getter方法：

```java
/**
 * 获取RedisTemplate，供其他组件使用
 */
public RedisTemplate<String, Object> getRedisTemplate() {
    return redisTemplate;
}
```

### 3. 增强错误处理

添加了详细的日志记录：

```java
log.info("检测到正在运行的内嵌任务，taskId: {}, userId: {}, taskType: {}", 
    existingTaskId, SecurityFrameworkUtils.getLoginUserId(), taskType.getTaskCode());
```

## 测试验证

### 1. 注解配置验证

```java
@Test
public void testAnnotationConfiguration() {
    var method = VopCategoryMappingController.class.getMethod("importExcel", MultipartFile.class);
    var asyncFrontTask = method.getAnnotation(AsyncFrontTask.class);
    
    assertNotNull(asyncFrontTask);
    assertEquals(AsyncTaskTypeEnum.PRODUCT_VOP_CATEGORY_MAPPING_IMPORT, asyncFrontTask.taskType());
    assertTrue(asyncFrontTask.returnRunningTaskId());
    assertFalse(asyncFrontTask.isNested());  // 验证isNested为false
}
```

### 2. 重复任务处理验证

```java
@Test
public void testReturnRunningTaskIdLogic() {
    String runningTaskId = "existing-task-456";
    String lockKey = AsyncFrontTask.genLockKey(userId, taskCode, type);
    
    // 模拟锁已存在，返回正在运行的taskId
    when(valueOperations.get(lockKey)).thenReturn(runningTaskId);
    
    Object result = valueOperations.get(lockKey);
    assertEquals(runningTaskId, result);
}
```

## 使用示例

### 1. 前端调用

```javascript
async function importData(file) {
    try {
        const response = await api.post('/mall/vop-category-mapping/import', { file });
        const taskId = response.data;
        
        console.log('任务ID:', taskId);
        // 开始轮询任务状态
        pollTaskStatus(taskId);
        
    } catch (error) {
        console.error('导入失败:', error);
    }
}
```

### 2. 预期行为

- **第一次点击**：创建新任务，返回新的taskId
- **重复点击**：返回正在运行的taskId（相同值）
- **任务完成后再点击**：创建新任务，返回新的taskId

## 注意事项

1. **isNested参数**：对于需要返回运行中taskId的异步任务，必须设置`isNested = false`
2. **锁机制**：锁值存储的是实际的taskId，便于查找正在运行的任务
3. **日志监控**：关注"检测到正在运行的任务"相关日志
4. **异常处理**：确保在各种异常情况下锁都能被正确释放

## 总结

通过这次修正，解决了重复任务处理的核心问题：

1. ✅ **正确配置注解**：`isNested = false`启用异步处理
2. ✅ **增强内嵌检查**：即使是内嵌执行也能检查重复任务
3. ✅ **完善错误处理**：添加详细日志和异常处理
4. ✅ **测试验证**：创建完整的测试用例验证功能

现在，导入京东分类映射Excel时，如果有任务正在运行，系统会正确返回正在运行的taskId，而不是抛出异常或执行错误的逻辑。
